"""数据查看组件。

提供数据展示、过滤、搜索等功能。
"""

import asyncio
import flet as ft
import json
import time
from typing import Dict, Any, List, Optional, Callable
from loguru import logger


class DataViewer(ft.Column):
    """数据查看器组件。"""

    def __init__(
        self,
        title: str = "数据查看器",
        on_refresh: Optional[Callable] = None,
        on_clear: Optional[Callable] = None,
    ):
        # 初始化列布局
        super().__init__(expand=True)
        
        self.title = title
        self.on_refresh = on_refresh
        self.on_clear = on_clear
        self.data = []
        self.filtered_data = []
        
        # 创建组件
        title_row = ft.Row(
            [
                ft.Text(title, size=20, weight=ft.FontWeight.BOLD),
                ft.Container(expand=True),
                ft.ElevatedButton(
                    "刷新",
                    icon=ft.Icons.REFRESH,
                    on_click=self._on_refresh_click,
                )
                if on_refresh
                else None,
                ft.ElevatedButton(
                    "清空",
                    icon=ft.Icons.CLEAR,
                    on_click=self._on_clear_click,
                )
                if on_clear
                else None,
            ],
            alignment=ft.MainAxisAlignment.START,
        )
        
        filter_row = ft.Row(
            [
                ft.TextField(
                    label="过滤",
                    hint_text="输入过滤条件...",
                    on_change=self._on_filter_change,
                    expand=True,
                ),
                ft.ElevatedButton(
                    "应用过滤",
                    on_click=self._apply_filter,
                ),
            ],
            spacing=10,
        )
        
        self.data_list = ft.ListView(
            expand=True, 
            spacing=10, 
            padding=10,
        )
        
        status_row = ft.Row(
            [
                ft.Text("数据更新时间: 无", italic=True),
                ft.Text("状态: 空闲")
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )
        
        # 将组件添加到列布局
        self.controls = [
            title_row,
            filter_row,
            ft.Divider(),
            ft.Container(
                content=self.data_list,
                # 移除 expand，保持固定高度
                border=ft.border.all(1, ft.Colors.GREY_300),
                border_radius=5,
                height=300,  # 设置明确高度
            ),
            ft.Divider(height=10, color=ft.Colors.TRANSPARENT),
            status_row
        ]

    def update_data(self, data_entries: List[Dict[str, Any]]):
        """更新数据。

        Args:
            data_entries: 数据条目列表
        """
        logger.info(f"DataViewer.update_data() 开始，接收到 {len(data_entries)} 条数据")
        self.data = data_entries
        logger.info("开始应用过滤条件")
        self._apply_filters()
        logger.info(f"过滤后数据条目: {len(self.filtered_data)}")
        logger.info("开始刷新数据列表")
        self._refresh_list()
        logger.info("开始更新状态")
        self._update_status()
        logger.info("开始更新时间戳")
        self._update_timestamp()
        logger.info("DataViewer.update_data() 完成")

    def _apply_filters(self):
        """应用过滤条件。"""
        filtered = self.data

        # 按插件ID过滤
        plugin_id_filter = self.controls[1].controls[0].value.strip()
        if plugin_id_filter:
            filtered = [
                entry
                for entry in filtered
                if plugin_id_filter.lower() in entry.get("plugin_id", "").lower()
            ]

        self.filtered_data = filtered

    def _refresh_list(self):
        """刷新数据列表。"""
        logger.info(f"开始刷新数据列表，过滤后条目数: {len(self.filtered_data)}")
        self.data_list.controls.clear()

        display_entries = self.filtered_data[-50:]  # 只显示最新的50条
        logger.info(f"将显示最新的 {len(display_entries)} 条数据")

        for i, entry in enumerate(display_entries):
            logger.debug(f"创建数据卡片 {i+1}: {entry.get('plugin_id', 'unknown')}")
            
            # 创建数据卡片
            card = self._create_data_card(entry, i+1)
            self.data_list.controls.append(card)  # 关键修复：将卡片添加到ListView
            
        logger.info(f"数据列表刷新完成，添加了 {len(display_entries)} 个控件")
        
        # 更新页面
        if hasattr(self, "page") and self.page:
            logger.info("调用page.update()更新数据列表")
            self.page.update()
            logger.info("数据列表页面更新完成")
        else:
            logger.warning("DataViewer没有page引用，无法更新页面")

    def _create_data_card(self, entry: Dict[str, Any], index: int) -> ft.Card:
        """创建数据卡片。

        Args:
            entry: 数据条目
            index: 数据卡片索引

        Returns:
            数据卡片组件
        """
        plugin_id = entry.get("plugin_id", "未知")
        data_type = entry.get("data_type", "未知")
        timestamp = entry.get("timestamp", 0)
        data = entry.get("data", {})
        metadata = entry.get("metadata", {})

        # 格式化时间
        time_str = time.strftime("%H:%M:%S", time.localtime(timestamp))

        # 格式化数据（限制长度）
        data_str = json.dumps(data, ensure_ascii=False, indent=2)
        if len(data_str) > 200:
            data_str = data_str[:200] + "..."

        # 创建元数据显示
        metadata_str = ""
        if metadata:
            metadata_str = f"元数据: {json.dumps(metadata, ensure_ascii=False)}"

        return ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Row(
                            [
                                ft.Text(
                                    f"{plugin_id}",
                                    size=14,
                                    weight=ft.FontWeight.BOLD,
                                ),
                                ft.Container(
                                    content=ft.Text(
                                        data_type,
                                        size=12,
                                        color=ft.Colors.WHITE,
                                    ),
                                    bgcolor=self._get_data_type_color(data_type),
                                    padding=ft.padding.symmetric(
                                        horizontal=8, vertical=2
                                    ),
                                    border_radius=10,
                                ),
                                ft.Container(expand=True),
                                ft.Text(
                                    time_str,
                                    size=12,
                                    color=ft.Colors.GREY_600,
                                ),
                            ]
                        ),
                        ft.Container(
                            content=ft.Text(
                                data_str,
                                size=12,
                                selectable=True,
                            ),
                            bgcolor=ft.Colors.GREY_100,
                            padding=10,
                            border_radius=5,
                        ),
                        ft.Text(
                            metadata_str,
                            size=10,
                            color=ft.Colors.GREY_500,
                        )
                        if metadata_str
                        else ft.Container(),
                    ],
                    spacing=5,
                ),
                padding=15,
            ),
        )

    def _get_data_type_color(self, data_type: str) -> str:
        """根据数据类型获取颜色。

        Args:
            data_type: 数据类型

        Returns:
            颜色值
        """
        color_map = {
            "input": ft.Colors.BLUE,
            "output": ft.Colors.GREEN,
            "processed": ft.Colors.ORANGE,
        }
        return color_map.get(data_type, ft.Colors.GREY)

    def _update_status(self):
        """更新状态文本。"""
        total_count = len(self.data)
        filtered_count = len(self.filtered_data)

        if total_count == 0:
            self.controls[-1].controls[0].value = "数据更新时间: 无"
            self.controls[-1].controls[1].value = "状态: 空闲"
        elif filtered_count == total_count:
            self.controls[-1].controls[0].value = f"数据更新时间: {time.strftime('%H:%M:%S')}"
            self.controls[-1].controls[1].value = f"状态: 共 {total_count} 条数据"
        else:
            self.controls[-1].controls[0].value = f"数据更新时间: {time.strftime('%H:%M:%S')}"
            self.controls[-1].controls[1].value = f"状态: 显示 {filtered_count} / {total_count} 条数据"

        # 更新页面
        if hasattr(self, "page") and self.page:
            self.page.update()

    def _update_timestamp(self):
        """更新时间戳。"""
        self.controls[-1].controls[0].value = f"数据更新时间: {time.strftime('%H:%M:%S')}"

        # 更新页面
        if hasattr(self, "page") and self.page:
            self.page.update()

    def _on_filter_change(self, e):
        """过滤条件变化事件。"""
        self._apply_filters()
        self._refresh_list()
        self._update_status()

    async def _on_refresh_click(self, e):
        """刷新按钮点击事件。"""
        logger.info("DataViewer刷新按钮被点击")
        if self.on_refresh:
            logger.info("开始执行刷新回调函数")
            try:
                if asyncio.iscoroutinefunction(self.on_refresh):
                    logger.info("执行异步刷新回调")
                    await self.on_refresh()
                else:
                    logger.info("执行同步刷新回调")
                    self.on_refresh()
                logger.info("刷新回调函数执行完成")
            except Exception as e:
                logger.error(f"执行刷新回调时出错: {e}")
                import traceback

                logger.error(f"错误详情: {traceback.format_exc()}")
        else:
            logger.warning("没有设置刷新回调函数")

    def _on_clear_click(self, e):
        """清除按钮点击事件。"""
        if self.on_clear:
            self.on_clear()

    def _apply_filter(self, e):
        """应用过滤按钮点击事件。"""
        self._apply_filters()
        self._refresh_list()
        self._update_status()
