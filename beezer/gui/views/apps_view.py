"""Beezer GUI Apps 配置视图。

负责显示和管理 Apps 配置的用户界面。
"""

import flet as ft
from typing import Dict, Any, Optional, List
from loguru import logger

from beezer.gui.config_manager import ConfigManager
from beezer.type_model import AppConfigModel
from beezer.apps.commands import AppType


class AppsView:
    """Apps 配置视图类。"""

    def __init__(self, config_manager: ConfigManager, page: ft.Page):
        """初始化 Apps 视图。

        Args:
            config_manager: 配置管理器
            page: Flet 页面对象
        """
        self.config_manager = config_manager
        self.page = page
        self.apps_list = ft.Column(spacing=10)
        
        # 编辑器相关
        self.current_editing_app = None
        self.edit_dialog = None

    def build(self) -> ft.Container:
        """构建 Apps 配置界面。

        Returns:
            ft.Container: Apps 配置界面容器
        """
        # 创建标题和添加按钮
        header = ft.Row(
            [
                ft.Text("Apps 配置", size=20, weight=ft.FontWeight.BOLD),
                ft.ElevatedButton(
                    "添加 App",
                    icon=ft.Icons.ADD,
                    on_click=self._on_add_app,
                ),
            ],
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
        )

        # 创建 Apps 列表容器
        apps_container = ft.Container(
            content=ft.Column(
                [
                    self.apps_list,
                ],
                scroll=ft.ScrollMode.AUTO,
                expand=True,
            ),
            padding=10,
            expand=True,
        )

        # 刷新 Apps 列表
        self._refresh_apps_list()

        return ft.Container(
            content=ft.Column(
                [
                    header,
                    ft.Divider(),
                    apps_container,
                ],
                expand=True,
            ),
            padding=20,
            expand=True,
        )

    def _refresh_apps_list(self):
        """刷新 Apps 列表。"""
        self.apps_list.controls.clear()

        apps = self.config_manager.get_apps()

        if not apps:
            self.apps_list.controls.append(
                ft.Container(
                    content=ft.Text(
                        "暂无 App 配置\n点击上方 '添加 App' 按钮开始配置",
                        text_align=ft.TextAlign.CENTER,
                        size=16,
                        color=ft.Colors.GREY_600,
                    ),
                    alignment=ft.alignment.center,
                    height=200,
                )
            )
        else:
            for app_config in apps:
                self.apps_list.controls.append(
                    self._create_app_card(app_config)
                )

        self.page.update()

    def _create_app_card(self, app_config: AppConfigModel) -> ft.Card:
        """创建 App 配置卡片。

        Args:
            app_config: App 配置对象

        Returns:
            ft.Card: App 配置卡片
        """
        # 创建参数显示
        args_text = ", ".join(str(arg) for arg in app_config.args) if app_config.args else "无"
        
        return ft.Card(
            content=ft.Container(
                content=ft.Column(
                    [
                        ft.Row(
                            [
                                ft.Icon(ft.Icons.APPS, size=24, color=ft.Colors.BLUE),
                                ft.Column(
                                    [
                                        ft.Text(
                                            app_config.name,
                                            size=16,
                                            weight=ft.FontWeight.BOLD,
                                        ),
                                        ft.Text(
                                            f"类型: {app_config.type}",
                                            size=12,
                                            color=ft.Colors.GREY_600,
                                        ),
                                    ],
                                    spacing=2,
                                    expand=True,
                                ),
                                ft.Row(
                                    [
                                        ft.IconButton(
                                            icon=ft.Icons.EDIT,
                                            tooltip="编辑",
                                            on_click=lambda e, app=app_config: self._on_edit_app(app),
                                        ),
                                        ft.IconButton(
                                            icon=ft.Icons.DELETE,
                                            tooltip="删除",
                                            icon_color=ft.Colors.RED,
                                            on_click=lambda e, app=app_config: self._on_delete_app(app),
                                        ),
                                    ],
                                    spacing=5,
                                ),
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Divider(height=1),
                        ft.Text(
                            f"参数: {args_text}",
                            size=12,
                            color=ft.Colors.GREY_700,
                        ),
                    ],
                    spacing=8,
                ),
                padding=15,
            ),
            margin=ft.margin.only(bottom=10),
        )

    def _on_add_app(self, e):
        """添加 App 按钮点击事件。"""
        self._show_app_editor()

    def _on_edit_app(self, app_config: AppConfigModel):
        """编辑 App 按钮点击事件。"""
        self._show_app_editor(app_config)

    def _on_delete_app(self, app_config: AppConfigModel):
        """删除 App 按钮点击事件。"""
        def confirm_delete(e):
            if self.config_manager.delete_app(app_config.name):
                self._refresh_apps_list()
                self.page.show_snack_bar(
                    ft.SnackBar(content=ft.Text(f"已删除 App: {app_config.name}"))
                )
            else:
                self.page.show_snack_bar(
                    ft.SnackBar(content=ft.Text(f"删除 App 失败: {app_config.name}"))
                )
            self.page.close(confirm_dialog)

        def cancel_delete(e):
            self.page.close(confirm_dialog)

        confirm_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("确认删除"),
            content=ft.Text(f"确定要删除 App '{app_config.name}' 吗？"),
            actions=[
                ft.TextButton("取消", on_click=cancel_delete),
                ft.TextButton("删除", on_click=confirm_delete),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )

        self.page.open(confirm_dialog)

    def _show_app_editor(self, app_config: Optional[AppConfigModel] = None):
        """显示 App 编辑器。

        Args:
            app_config: 要编辑的 App 配置，None 表示添加新 App
        """
        is_edit_mode = app_config is not None
        title = f"编辑 App: {app_config.name}" if is_edit_mode else "添加 App"

        # 创建表单字段
        name_field = ft.TextField(
            label="App 名称",
            value=app_config.name if is_edit_mode else "",
            width=300,
        )

        # App 类型下拉选择
        app_type_options = [ft.dropdown.Option(app_type.value) for app_type in AppType]
        type_field = ft.Dropdown(
            label="App 类型",
            options=app_type_options,
            value=app_config.type if is_edit_mode else AppType.CUSTOM.value,
            width=300,
        )

        # 参数输入（简化为文本输入，用逗号分隔）
        args_text = ", ".join(str(arg) for arg in app_config.args) if is_edit_mode and app_config.args else ""
        args_field = ft.TextField(
            label="参数 (用逗号分隔)",
            value=args_text,
            width=300,
            multiline=True,
            min_lines=2,
            max_lines=4,
        )

        def save_app(e):
            try:
                # 解析参数
                args_list = []
                if args_field.value.strip():
                    args_list = [arg.strip() for arg in args_field.value.split(",") if arg.strip()]

                # 创建 App 配置对象
                new_app_config = AppConfigModel(
                    name=name_field.value.strip(),
                    type=type_field.value,
                    args=args_list,
                )

                # 保存配置
                if is_edit_mode:
                    success = self.config_manager.update_app(app_config.name, new_app_config)
                    action = "更新"
                else:
                    success = self.config_manager.add_app(new_app_config)
                    action = "添加"

                if success:
                    self._refresh_apps_list()
                    self.page.show_snack_bar(
                        ft.SnackBar(content=ft.Text(f"成功{action} App: {new_app_config.name}"))
                    )
                    self.page.close(self.edit_dialog)
                else:
                    self.page.show_snack_bar(
                        ft.SnackBar(content=ft.Text(f"{action} App 失败"))
                    )

            except Exception as ex:
                logger.error(f"保存 App 配置失败: {ex}")
                self.page.show_snack_bar(
                    ft.SnackBar(content=ft.Text(f"保存失败: {str(ex)}"))
                )

        def cancel_edit(e):
            self.page.close(self.edit_dialog)

        # 创建编辑对话框
        self.edit_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text(title),
            content=ft.Container(
                content=ft.Column(
                    [
                        name_field,
                        type_field,
                        args_field,
                    ],
                    spacing=15,
                    tight=True,
                ),
                width=400,
                height=300,
            ),
            actions=[
                ft.TextButton("取消", on_click=cancel_edit),
                ft.ElevatedButton("保存", on_click=save_app),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )

        self.page.open(self.edit_dialog)
