#!/usr/bin/env python3
"""测试Apps配置功能的脚本。"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from beezer.gui.config_manager import ConfigManager
from beezer.type_model import AppConfigModel
from beezer.apps.commands import AppType


async def test_apps_config():
    """测试Apps配置功能。"""
    print("开始测试Apps配置功能...")
    
    # 创建配置管理器
    config_manager = ConfigManager()
    
    # 加载配置
    print("加载配置文件...")
    success = await config_manager.load_config()
    if not success:
        print("配置加载失败！")
        return False
    
    print("配置加载成功！")
    
    # 获取当前Apps配置
    apps = config_manager.get_apps()
    print(f"当前Apps数量: {len(apps)}")
    
    for i, app in enumerate(apps):
        print(f"  App {i+1}: {app.name} (类型: {app.type}, 参数: {app.args})")
    
    # 测试添加新App
    print("\n测试添加新App...")
    new_app = AppConfigModel(
        name="test_custom_app",
        type=AppType.CUSTOM.value,
        args=["--config", "/path/to/config", "--verbose"]
    )
    
    success = config_manager.add_app(new_app)
    if success:
        print("添加App成功！")
    else:
        print("添加App失败！")
        return False
    
    # 再次获取Apps配置
    apps = config_manager.get_apps()
    print(f"添加后Apps数量: {len(apps)}")
    
    # 测试更新App
    print("\n测试更新App...")
    updated_app = AppConfigModel(
        name="test_custom_app_updated",
        type=AppType.FANUC.value,
        args=["--host", "*************", "--port", "8193"]
    )
    
    success = config_manager.update_app("test_custom_app", updated_app)
    if success:
        print("更新App成功！")
    else:
        print("更新App失败！")
        return False
    
    # 测试删除App
    print("\n测试删除App...")
    success = config_manager.delete_app("test_custom_app_updated")
    if success:
        print("删除App成功！")
    else:
        print("删除App失败！")
        return False
    
    # 最终检查
    apps = config_manager.get_apps()
    print(f"最终Apps数量: {len(apps)}")
    
    print("\nApps配置功能测试完成！")
    return True


async def test_app_types():
    """测试App类型枚举。"""
    print("\n测试App类型枚举...")
    
    print("可用的App类型:")
    for app_type in AppType:
        print(f"  - {app_type.value}")
    
    print(f"总共 {len(AppType)} 种App类型")


if __name__ == "__main__":
    async def main():
        try:
            await test_app_types()
            success = await test_apps_config()
            if success:
                print("\n✅ 所有测试通过！")
            else:
                print("\n❌ 测试失败！")
                sys.exit(1)
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    
    asyncio.run(main())
